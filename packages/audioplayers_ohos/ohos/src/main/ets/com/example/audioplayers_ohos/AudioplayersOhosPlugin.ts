
import { FlutterAbility, FlutterEng<PERSON>, <PERSON><PERSON><PERSON>nel, <PERSON>Call, MethodResult } from '@ohos/flutter_ohos';
import { OHOSAudioPlayer } from './OHOSAudioPlayer';

export class AudioplayersOhosPlugin {
  private players: Map<string, OHOSAudioPlayer> = new Map();
  private channel: MethodChannel | null = null;

  public registerWith(flutterEngine: FlutterEngine) {
    const channelName = 'com.example.audioplayers_ohos';
    this.channel = new MethodChannel(flutterEngine.dartExecutor, channelName);

    this.channel.setMethodCallHandler({
      onMethodCall: async (call: MethodCall, result: MethodResult) => {
        const playerId = call.arguments['playerId'];
        if (!playerId) {
          result.error('400', 'playerId is required', null);
          return;
        }

        let player = this.players.get(playerId);
        if (!player) {
          player = new OHOSAudioPlayer((event, value) => {
            this.channel?.invokeMethod('onEvent', { ...value, playerId, event });
          });
          this.players.set(playerId, player);
        }

        switch (call.method) {
          case 'play':
            await player.play();
            result.success(null);
            break;
          case 'pause':
            await player.pause();
            result.success(null);
            break;
          case 'resume':
            await player.resume();
            result.success(null);
            break;
          case 'stop':
            await player.stop();
            result.success(null);
            break;
          case 'release':
            await player.release();
            this.players.delete(playerId);
            result.success(null);
            break;
          case 'seek':
            const position = call.arguments['position'];
            await player.seek(position);
            result.success(null);
            break;
          case 'setSourceUrl':
            const url = call.arguments['url'];
            await player.setSourceUrl(url);
            result.success(null);
            break;
          case 'setVolume':
            const volume = call.arguments['volume'];
            await player.setVolume(volume);
            result.success(null);
            break;
          case 'setPlaybackRate':
            const rate = call.arguments['rate'];
            await player.setPlaybackRate(rate);
            result.success(null);
            break;
          case 'getDuration':
            const duration = player.getDuration();
            result.success(duration);
            break;
          case 'getCurrentPosition':
            const currentPosition = player.getCurrentPosition();
            result.success(currentPosition);
            break;
          default:
            result.notImplemented();
        }
      },
    });
  }
}
