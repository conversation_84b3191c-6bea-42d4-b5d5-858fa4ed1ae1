{"module": {"name": "audioplayers_ohos", "type": "har", "description": "OpenHarmony implementation of audioplayers plugin", "mainElement": "", "deviceTypes": ["phone", "tablet", "2in1"], "deliveryWithInstall": true, "installationFree": false, "pages": "", "abilities": [], "extensionAbilities": [], "requestPermissions": [{"name": "ohos.permission.INTERNET", "reason": "Access network to play audio from URLs", "usedScene": {"abilities": [], "when": "inuse"}}, {"name": "ohos.permission.READ_MEDIA", "reason": "Read media files for audio playback", "usedScene": {"abilities": [], "when": "inuse"}}], "metadata": [{"name": "ArkTSPartialUpdate", "value": "false"}]}}